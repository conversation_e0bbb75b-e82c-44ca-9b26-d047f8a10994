<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:09:16
  from 'C:\xampp\htdocs\templates\widdx\frontend\inc\widdx-header-theme-switcher.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68800c0c5c57a6_70835168',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '71333ee83e6b7329e61ed5feeb7403f8437e5c8a' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\frontend\\inc\\widdx-header-theme-switcher.tpl',
      1 => 1747703290,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68800c0c5c57a6_70835168 (Smarty_Internal_Template $_smarty_tpl) {
?><!-- Header Theme Toggle Button -->
<div class="ww-color-switch-header">
  <button type="button" class="ww-theme-toggle-header" aria-label="Toggle Dark/Light Mode" id="header-theme-toggle-btn">
    <div class="ww-theme-light" data-title="Dark">
      <i class="fas fa-moon"></i>
    </div>
    <div class="ww-theme-dark" data-title="Light">
      <i class="fas fa-sun"></i>
    </div>
  </button>
</div>

<!-- Inline script for immediate theme toggle functionality -->
<?php echo '<script'; ?>
>
  // Immediate theme toggle functionality
  document.addEventListener('DOMContentLoaded', function() {
    var toggleBtn = document.getElementById('header-theme-toggle-btn');
    if (toggleBtn) {
      toggleBtn.onclick = function(e) {
        e.preventDefault();
        var html = document.documentElement;
        var currentTheme = html.getAttribute('data-bs-theme') || 'light';
        var newTheme = currentTheme === 'light' ? 'dark' : 'light';

        html.setAttribute('data-bs-theme', newTheme);
        document.body.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);

        console.log('Theme toggled to:', newTheme);
      };
    }
  });
<?php echo '</script'; ?>
>
<?php }
}
