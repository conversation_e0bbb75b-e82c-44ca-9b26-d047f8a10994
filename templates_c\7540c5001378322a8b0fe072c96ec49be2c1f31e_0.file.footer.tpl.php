<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:09:16
  from 'C:\xampp\htdocs\templates\widdx\footer.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68800c0c982bb5_45949982',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '7540c5001378322a8b0fe072c96ec49be2c1f31e' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\footer.tpl',
      1 => 1726688540,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68800c0c982bb5_45949982 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['loggedin']->value) {?>
    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/partial/widdx-footer.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
} else { ?>
    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/inc/widdx-footer.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
}
}
}
