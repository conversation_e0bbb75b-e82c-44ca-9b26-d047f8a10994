<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:09:16
  from 'C:\xampp\htdocs\templates\widdx\frontend\inc\social-accounts.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68800c0c6a6f77_64222766',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '173b5491e97c8538d1390682abda15800a4501f0' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\frontend\\inc\\social-accounts.tpl',
      1 => **********,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68800c0c6a6f77_64222766 (Smarty_Internal_Template $_smarty_tpl) {
?><ul class="list-inline social-icons">
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['socialAccounts']->value, 'account');
$_smarty_tpl->tpl_vars['account']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['account']->value) {
$_smarty_tpl->tpl_vars['account']->do_else = false;
?>
        <li class="list-inline-item">
            <a class="btn  btn-icon rounded-circle p-2" href="<?php echo $_smarty_tpl->tpl_vars['account']->value->getUrl();?>
" target="_blank">
                <i class="<?php echo $_smarty_tpl->tpl_vars['account']->value->getFontAwesomeIcon();?>
"></i>
            </a>
        </li>
    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</ul>
<style>

</style><?php }
}
