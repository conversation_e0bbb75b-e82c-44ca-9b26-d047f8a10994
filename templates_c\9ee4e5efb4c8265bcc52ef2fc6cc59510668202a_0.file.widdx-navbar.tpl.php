<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:09:15
  from 'C:\xampp\htdocs\templates\widdx\frontend\inc\widdx-navbar.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68800c0baada22_35833606',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '9ee4e5efb4c8265bcc52ef2fc6cc59510668202a' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\frontend\\inc\\widdx-navbar.tpl',
      1 => 1724055444,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68800c0baada22_35833606 (Smarty_Internal_Template $_smarty_tpl) {
?>    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['navbar']->value, 'item');
$_smarty_tpl->tpl_vars['item']->index = -1;
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
$_smarty_tpl->tpl_vars['item']->index++;
$_smarty_tpl->tpl_vars['item']->first = !$_smarty_tpl->tpl_vars['item']->index;
$__foreach_item_0_saved = $_smarty_tpl->tpl_vars['item'];
?>
        <li class="nav-item<?php if ($_smarty_tpl->tpl_vars['item']->first) {?> active<?php }
if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?> dropdown<?php }
if ($_smarty_tpl->tpl_vars['item']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['item']->value->getClass();
}?>"
            id="<?php echo $_smarty_tpl->tpl_vars['item']->value->getId();?>
">
            <a class="nav-link<?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?> dropdown-toggle<?php }?>"
                href="<?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>#<?php } else {
echo $_smarty_tpl->tpl_vars['item']->value->getUri();
}?>"
                id="<?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>navbarDropdown<?php echo $_smarty_tpl->tpl_vars['item']->value->getId();
} else {
}?>" role="button"
                data-toggle="<?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>dropdown<?php }?>" aria-haspopup="true" aria-expanded="false"
                <?php if ($_smarty_tpl->tpl_vars['item']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['item']->value->getAttribute('target');?>
" <?php }?>>
                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasIcon()) {?><i class="<?php echo $_smarty_tpl->tpl_vars['item']->value->getIcon();?>
"></i><?php }?>
                <?php echo $_smarty_tpl->tpl_vars['item']->value->getLabel();?>

                <?php if ($_smarty_tpl->tpl_vars['item']->value->hasBadge()) {?><span class="badge badge-pill badge-primary ml-2"><?php echo $_smarty_tpl->tpl_vars['item']->value->getBadge();?>
</span><?php }?>
            </a>
            <?php if ($_smarty_tpl->tpl_vars['item']->value->hasChildren()) {?>
                <div class="dropdown-menu<?php if ((isset($_smarty_tpl->tpl_vars['rightDrop']->value)) && $_smarty_tpl->tpl_vars['rightDrop']->value) {?> dropdown-menu-right<?php }?>"
                    aria-labelledby="navbarDropdown<?php echo $_smarty_tpl->tpl_vars['item']->value->getId();?>
">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['item']->value->getChildren(), 'childItem');
$_smarty_tpl->tpl_vars['childItem']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['childItem']->value) {
$_smarty_tpl->tpl_vars['childItem']->do_else = false;
?>
                        <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass() && in_array($_smarty_tpl->tpl_vars['childItem']->value->getClass(),array('dropdown-divider','nav-divider'))) {?>
                            <div class="dropdown-divider"></div>
                        <?php } else { ?>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getUri();?>
"
                                class="dropdown-item<?php if ($_smarty_tpl->tpl_vars['childItem']->value->getClass()) {?> <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getClass();
}?>"
                                <?php if ($_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target')) {?> target="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getAttribute('target');?>
" <?php }?>>
                                <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasIcon()) {?><i class="<?php echo $_smarty_tpl->tpl_vars['childItem']->value->getIcon();?>
"></i><?php }?>
                                <?php echo $_smarty_tpl->tpl_vars['childItem']->value->getLabel();?>

                                <?php if ($_smarty_tpl->tpl_vars['childItem']->value->hasBadge()) {?><span
                                    class="badge badge-pill badge-secondary ml-2"><?php echo $_smarty_tpl->tpl_vars['childItem']->value->getBadge();?>
</span><?php }?>
                            </a>
                        <?php }?>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </div>
            <?php }?>
        </li>
    <?php
$_smarty_tpl->tpl_vars['item'] = $__foreach_item_0_saved;
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);
}
}
