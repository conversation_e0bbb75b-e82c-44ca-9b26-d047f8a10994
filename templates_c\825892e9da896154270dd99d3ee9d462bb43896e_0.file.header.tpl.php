<?php
/* Smarty version 3.1.48, created on 2025-07-23 00:09:12
  from 'C:\xampp\htdocs\templates\widdx\header.tpl' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '3.1.48',
  'unifunc' => 'content_68800c089d0cc3_46419697',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '825892e9da896154270dd99d3ee9d462bb43896e' => 
    array (
      0 => 'C:\\xampp\\htdocs\\templates\\widdx\\header.tpl',
      1 => 1749673454,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_68800c089d0cc3_46419697 (Smarty_Internal_Template $_smarty_tpl) {
if ($_smarty_tpl->tpl_vars['loggedin']->value) {?>
    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/backend/widdx-layout.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
} else { ?>
    <?php $_smarty_tpl->_subTemplateRender(((string)$_smarty_tpl->tpl_vars['template']->value)."/frontend/widdx-layout.tpl", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, true);
?>

<?php }
}
}
